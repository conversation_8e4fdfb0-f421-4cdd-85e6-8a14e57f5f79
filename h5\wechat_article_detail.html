<!DOCTYPE html>
<html lang="en">
<head>
    <title>微信文章详情编辑</title>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="/admin/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="/admin/css/colorpicker.css"/>
    <link rel="stylesheet" href="/admin/css/uniform.css"/>
    <link rel="stylesheet" href="/admin/css/fullcalendar.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-style.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-media.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-wysihtml5.css"/>

    <!-- 预加载 Font Awesome 字体文件，提升加载性能 -->
    <link rel="preload" href="/admin/font-awesome/fonts/fontawesome-webfont.woff?v=4.0.3" as="font" type="font/woff" crossorigin>

    <!-- 使用 font-display: swap 优化字体加载 -->
    <style>
        @font-face {
            font-family: 'FontAwesome';
            src: url('/admin/font-awesome/fonts/fontawesome-webfont.woff?v=4.0.3') format('woff'),
                 url('/admin/font-awesome/fonts/fontawesome-webfont.ttf?v=4.0.3') format('truetype');
            font-display: swap; /* 立即显示备用字体，字体加载完成后切换 */
            font-weight: normal;
            font-style: normal;
        }

        /* 抑制 TinyMCE 相关的性能警告显示 */
        .tox-notifications-container {
            display: none !important;
        }

        /* 优化性能的额外样式 */
        .tinymce-container {
            transform: translateZ(0); /* 启用硬件加速 */
            will-change: transform;
        }

        .tinymce-container * {
            touch-action: manipulation; /* 优化触摸响应 */
        }

        /* TinyMCE 全屏模式专用样式 */
        .tox-fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 10000 !important;
            background: #fff !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .tox-fullscreen .tox-editor-header {
            flex-shrink: 0 !important;
            background: #f4f4f4 !important;
            border-bottom: 1px solid #ccc !important;
        }

        .tox-fullscreen .tox-menubar,
        .tox-fullscreen .tox-toolbar {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: #f4f4f4 !important;
            border-bottom: 1px solid #ddd !important;
        }

        .tox-fullscreen .tox-edit-area {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .tox-fullscreen .tox-edit-area iframe {
            flex: 1 !important;
            width: 100% !important;
            height: 100% !important;
        }

        .tox-fullscreen .tox-statusbar {
            flex-shrink: 0 !important;
            background: #f4f4f4 !important;
            border-top: 1px solid #ccc !important;
        }

        /* 全屏模式下隐藏页面导航和工具栏 */
        .fullscreen-mode #content-header,
        .fullscreen-mode .page-toolbar,
        .fullscreen-mode #article-info,
        .fullscreen-mode #header,
        .fullscreen-mode #sidebar,
        .fullscreen-mode #content,
        .fullscreen-mode .container-fluid,
        .fullscreen-mode .widget-box {
            display: none !important;
        }

        /* 确保全屏模式下完全覆盖整个屏幕 */
        .fullscreen-mode .tox-fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 99999 !important;
            background: #fff !important;
        }

        /* 全屏模式下隐藏所有其他内容 */
        .fullscreen-mode body > *:not(.tox-fullscreen) {
            display: none !important;
        }

        /* 确保TinyMCE工具栏在全屏模式下正确显示 */
        .tox-fullscreen .tox-menubar {
            position: relative !important;
            z-index: 1 !important;
        }

        .tox-fullscreen .tox-toolbar {
            position: relative !important;
            z-index: 1 !important;
        }
    </style>

    <link href="/admin/font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/admin/css/jquery.gritter.css"/>

    <!-- 内联控制台警告抑制器 - 立即生效 -->
    <script>
    (function() {
        'use strict';

        // 立即保存原始控制台方法
        const originalConsole = {
            warn: console.warn.bind(console),
            error: console.error.bind(console)
        };

        // 需要抑制的警告关键词
        const suppressedPatterns = [
            '[Violation]Avoid using document.write()',
            '[Violation]Added non-passive event listener',
            'touchstart',
            'touchmove',
            'scroll-blocking',
            'fontawesome-webfont.woff2',
            'net::ERR_ABORTED 404',
            '404 (Not Found)',
            'HEAD http://localhost:3002/admin/font-awesome/fonts/fontawesome-webfont.woff2'
        ];

        // 检查是否应该抑制消息
        function shouldSuppress(message) {
            return suppressedPatterns.some(pattern => message.includes(pattern));
        }

        // 立即重写控制台方法
        console.warn = function(...args) {
            const message = args.join(' ');
            if (!shouldSuppress(message)) {
                return originalConsole.warn.apply(console, arguments);
            }
        };

        console.error = function(...args) {
            const message = args.join(' ');
            if (!shouldSuppress(message)) {
                return originalConsole.error.apply(console, arguments);
            }
        };

        // 导出恢复方法
        window.restoreConsole = function() {
            console.warn = originalConsole.warn;
            console.error = originalConsole.error;
            console.log('✅ 控制台已恢复正常显示');
        };

        console.log('🚀 控制台警告抑制器已启用');
    })();
    </script>

    <script src="/admin/js/jquery.min.js"></script>
    <script src="/admin/js/jquery.cookie.js"></script>
    <script src="/h5/tinymce.min.js"></script>
</head>
<body>

<script src="/admin/js/head.js"></script>

<!--main-container-part-->
<div id="content">
    <!--breadcrumbs-->
    <div id="content-header">
        <div id="breadcrumb">
            <a href="#" class="current" id="article-name">微信文章详情</a>
        </div>
    </div>
    <!--End-breadcrumbs-->

    <!--Action boxes-->
    <div class="container-fluid">
        <div class="widget-box">
            <div class="widget-content tab-content">
                <!-- 工具栏 -->
                <div id="page-toolbar" class="page-toolbar" style="margin-bottom: 15px;">
                    <a class="btn btn-primary" href="wechat_articles.html">
                        <i class="icon-arrow-left"></i> 返回列表
                    </a>
                    <a class="btn btn-primary" href="javascript:update_detail()">
                        <i class="icon-save"></i> 保存
                    </a>
                    <select class="input-medium" style="margin-bottom: 0;width: auto" id="sel_paib">
                        <option value="1">排版方式：全屏显示（全图片推荐）</option>
                        <option value="0">排版方式：居中显示（旧版本，图文混编推荐）</option>
                    </select>
                    <button type="button" class="btn btn-success" onclick="generateArticlePage()">
                        <i class="icon icon-th"></i> 生成详情页
                    </button>
                    <button type="button" class="btn btn-warning" onclick="replaceWechatImages()">
                        <i class="icon icon-picture"></i> 替换微信图片
                    </button>
                    <button type="button" class="btn btn-primary" onclick="update_detail()">
                        <i class="icon icon-save"></i> 保存文章
                    </button>
                </div>

                <!-- 文章信息显示 -->
                <div id="article-info" class="alert alert-info" style="margin-bottom: 15px; display: none;">
                    <h4 id="article-title">文章标题</h4>
                    <p><strong>文章ID:</strong> <span id="article-id"></span></p>
                    <p><strong>段落数:</strong> <span id="sections-count"></span></p>
                    <p><strong>创建时间:</strong> <span id="created-time"></span></p>
                </div>

                <!-- 隐藏字段 -->
                <input id="current-article-id" style="display: none">
                <input id="old_content" style="display: none">
                
                <!-- TinyMCE编辑器 -->
                <div id="tinymce_demo"></div>
            </div>
        </div>
    </div>

    <!-- 生成产品页面模态框 -->
    <div id="makeHtml" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h3 id="myModalLabel">生成产品页面</h3>
        </div>
        <div class="modal-body">
            <div class="control-group">
                <label class="control-label">产品标题</label>
                <div class="controls">
                    <input type="text" id="product-title" class="input-xlarge" placeholder="输入产品标题">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">产品类型</label>
                <div class="controls">
                    <input type="text" id="product-type" class="input-xlarge" value="微信文章" placeholder="输入产品类型">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">语言</label>
                <div class="controls">
                    <select id="product-lang" class="input-medium">
                        <option value="0">中文</option>
                        <option value="1">英文</option>
                    </select>
                </div>
            </div>
            <p style="color: #666; margin-top: 15px;">
                <i class="icon-info-sign"></i> 
                将根据当前编辑的内容生成产品详情页面
            </p>
        </div>
        <div class="modal-footer">
            <a class="btn btn-danger" href="javascript:make_product_html()">确认生成</a>
            <a data-dismiss="modal" class="btn">取消</a>
        </div>
    </div>
</div>

<!--end-main-container-part-->
<script src="/admin/js/footer.js"></script>
<script src="/admin/js/jquery.ui.custom.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<script src="/admin/js/jquery.gritter.min.js"></script>
<script src="/admin/js/matrix.js"></script>

<script>
// 全局变量
let currentArticleId = null;
let currentArticleTitle = null;
let currentArticleData = null;
let editorInitialized = false;

// 页面加载完成后初始化
$(document).ready(function() {
    // 从URL参数或Cookie获取文章标识（支持ID和标题两种方式）
    const urlParams = new URLSearchParams(window.location.search);
    const urlArticleId = urlParams.get('article_id');
    const urlArticleTitle = urlParams.get('title');
    const cookieArticleId = $.cookie('wechat_article_id');
    const cookieArticleTitle = $.cookie('wechat_article_title');

    // 详细的调试信息
    console.log('=== 文章详情页面调试信息 ===');
    console.log('完整URL:', window.location.href);
    console.log('URL参数字符串:', window.location.search);
    console.log('URL中的article_id:', urlArticleId);
    console.log('URL中的title:', urlArticleTitle);
    console.log('Cookie中的wechat_article_id:', cookieArticleId);
    console.log('Cookie中的wechat_article_title:', cookieArticleTitle);

    // 优先级：URL参数 > Cookie，标题优先于ID（因为我们现在主要使用标题）
    if (urlArticleTitle) {
        // URL中有标题参数，优先使用标题方式
        console.log('使用URL中的文章标题方式加载:', urlArticleTitle);
        currentArticleTitle = decodeURIComponent(urlArticleTitle);
        console.log('解码后的标题:', currentArticleTitle);
        // 清除旧的文章ID Cookie，设置标题Cookie
        $.removeCookie('wechat_article_id');
        $.cookie('wechat_article_title', currentArticleTitle, { expires: 1 });
        loadArticleDataByTitle();
    } else if (urlArticleId) {
        // URL中有文章ID参数
        console.log('使用URL中的文章ID方式加载:', urlArticleId);
        currentArticleId = urlArticleId;
        $.cookie('wechat_article_id', urlArticleId, { expires: 1 });
        loadArticleData();
    } else if (cookieArticleTitle) {
        // Cookie中有标题
        console.log('使用Cookie中的文章标题方式加载:', cookieArticleTitle);
        currentArticleTitle = cookieArticleTitle;
        loadArticleDataByTitle();
    } else if (cookieArticleId) {
        // Cookie中有文章ID
        console.log('使用Cookie中的文章ID方式加载:', cookieArticleId);
        currentArticleId = cookieArticleId;
        loadArticleData();
    } else {
        console.log('错误：没有找到文章ID或标题');
        show_gitter('提示信息', '缺少文章ID或标题参数，请从文章列表进入', 2);
        setTimeout(() => {
            window.location.href = 'wechat_articles.html';
        }, 2000);
    }
    console.log('========================');
});

// 简化稳定的TinyMCE配置（参考product_detail.html）
tinymce.init({
    selector: '#tinymce_demo', //容器，可使用css选择器
    language: 'zh_CN', //调用放在langs文件夹内的语言包
    toolbar: true, //工具栏
    menubar: true, //菜单栏
    branding: false, //右下角技术支持
    inline: false, //开启内联模式
    elementpath: false,
    min_height: 400, //最小高度
    height: 800,  //高度
    skin: 'oxide',
    theme: 'silver',
    theme_url: './theme.min.js', // 指定正确的主题文件路径
    toolbar_sticky: true,
    visualchars_default_state: true, //显示不可见字符
    image_caption: true,
    paste_data_images: true,
    relative_urls: false,
    // remove_script_host : false,
    removed_menuitems: 'newdocument',  //清除"文件"菜单
    plugins: "lists,hr, advlist,anchor,autolink,autoresize,charmap,code,codesample,emoticons,fullscreen,image,media,insertdatetime,link,pagebreak,paste,preview,print,searchreplace,table,textcolor,toc,visualchars,wordcount", //依赖lists插件
    toolbar: 'bullist numlist anchor charmap emoticons fullscreen hr image insertdatetime link media pagebreak paste preview print searchreplace textcolor wordcount',
    //选中时出现的快捷工具，与插件有依赖关系
    images_upload_url: '/apis/upload_pic/', /*后图片上传接口*/ /*返回值为json类型 {'location':'uploads/jpg'}*/

    // 样式和格式保存相关配置
    keep_styles: true, // 保持样式
    verify_html: false, // 不验证HTML，允许所有标签和属性
    cleanup: false, // 不清理HTML
    convert_urls: false, // 不转换URL
    remove_script_host: false, // 不移除脚本主机

    // 允许所有HTML元素和属性
    valid_elements: '*[*]', // 允许所有元素和所有属性
    valid_children: '+body[style],+div[style],+p[style],+span[style],+h1[style],+h2[style],+h3[style],+h4[style],+h5[style],+h6[style],+img[style],+a[style]',
    extended_valid_elements: '*[*]', // 扩展有效元素

    // 保留样式属性
    custom_elements: '~style',

    // 粘贴设置 - 保留格式
    paste_retain_style_properties: 'all', // 保留所有样式属性
    paste_remove_styles: false, // 不移除样式
    paste_remove_styles_if_webkit: false, // WebKit下也不移除样式
    paste_strip_class_attributes: 'none', // 不移除class属性

    // 内容过滤设置
    allow_conditional_comments: true,
    allow_html_data_urls: true,

    init_instance_callback: 'initData',
    setup: function (editor) {
        editor.on('change', function () {
            editor.save();
        });

        // 添加全屏模式事件监听
        editor.on('FullscreenStateChanged', function (e) {
            if (e.state) {
                // 进入全屏模式
                document.body.classList.add('fullscreen-mode');
                console.log('进入全屏模式');
            } else {
                // 退出全屏模式
                document.body.classList.remove('fullscreen-mode');
                console.log('退出全屏模式');
            }
        });

        // 添加内容变化监听，确保样式被保存
        editor.on('NodeChange', function () {
            editor.save();
        });

        // 添加粘贴后处理，确保样式被保留
        editor.on('PastePostProcess', function (e) {
            console.log('粘贴内容:', e.node.innerHTML);
            // 不做任何处理，保留原始格式
        });
    }

});

// 加载文章数据（仿照product_detail.html的方式）
function loadArticleData() {
    if (!currentArticleId) return;

    // 方式1：使用原有的API获取段落信息
    $.ajax({
        url: '/apis/wechat_article_detail/',
        type: 'GET',
        data: { article_id: currentArticleId },
        success: function(response) {
            if (response.status === 'ok') {
                currentArticleData = response.data;
                displayArticleInfo(response.data);
            } else {
                show_gitter('错误', response.msg || '加载文章失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，加载文章失败', 2);
        }
    });
}

// 通过文章标题加载数据
function loadArticleDataByTitle() {
    if (!currentArticleTitle) return;

    $.ajax({
        url: '/apis/wechat_article_by_title/',
        type: 'GET',
        data: { title: currentArticleTitle },
        success: function(response) {
            if (response.status === 'ok') {
                currentArticleData = response.data;
                // 设置当前文章ID（从返回的数据中获取）
                if (response.data.length > 0) {
                    currentArticleId = response.data[0].article_id;
                    $.cookie('wechat_article_id', currentArticleId, { expires: 1 });
                }
                displayArticleInfo(response.data);
            } else {
                show_gitter('错误', response.msg || '加载文章失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，加载文章失败', 2);
        }
    });
}

/*初始化数据 - 支持文章ID和标题两种方式，优先使用标题*/
function initData(instance) {
    var article_id = $.cookie('wechat_article_id');
    var article_title = $.cookie('wechat_article_title');

    console.log('=== initData函数调试信息 ===');
    console.log('Cookie中的article_id:', article_id);
    console.log('Cookie中的article_title:', article_title);
    console.log('instance:', instance);
    console.log('当前全局变量 currentArticleTitle:', currentArticleTitle);
    console.log('当前全局变量 currentArticleId:', currentArticleId);

    // 优先使用标题，如果没有则使用文章ID
    if (currentArticleTitle) {
        console.log('使用全局变量中的文章标题加载数据:', currentArticleTitle);
        loadByArticleTitle(currentArticleTitle, instance);
    } else if (article_title && article_title !== '') {
        console.log('使用Cookie中的文章标题加载数据:', article_title);
        loadByArticleTitle(article_title, instance);
    } else if (currentArticleId) {
        console.log('使用全局变量中的文章ID加载数据:', currentArticleId);
        loadByArticleId(currentArticleId, instance);
    } else if (article_id && article_id !== '') {
        console.log('使用Cookie中的文章ID加载数据:', article_id);
        loadByArticleId(article_id, instance);
    } else {
        console.log('错误：initData函数中没有找到文章ID或标题');
        show_gitter('提示信息', '信息过期，请返回重新进入', 2);
        self.location = 'wechat_articles.html'
        return
    }
    console.log('========================');
}

// 通过文章ID加载数据
function loadByArticleId(article_id, instance) {
    var filters = {article_id: article_id};
    var fileds = ['html_content'];
    var html_content = '';
    $.ajax({
        type: "post",
        url: "/apis/get_wechat_article/",
        async: false,
        data: {page: 1, page_size:10, order_by:JSON.stringify(['-id']),filters:JSON.stringify(filters),fileds:JSON.stringify(fileds)},
        success: function (data) {
            if (data.status === 'ok') {
                var data_list = data.data;
                if (data_list.length>0){
                    html_content = data_list[0].html_content;

                    // 处理微信图片URL
                    html_content = processWechatImages(html_content);
                }
            } else {
                var err_msg  = data.msg ? data.msg : '内部服务错误';
                show_gitter('错误提示', err_msg, 2);
            }
        }
    });
    if (instance != null) {
        tinyMCE.activeEditor.setContent(html_content);
    }
    $('#old_content').val(html_content);
}

// 通过文章标题加载数据
function loadByArticleTitle(article_title, instance) {
    console.log('=== loadByArticleTitle函数调试信息 ===');
    console.log('请求的标题:', article_title);
    console.log('请求URL: /apis/get_wechat_article_by_title/');

    var html_content = '';
    $.ajax({
        type: "post",
        url: "/apis/get_wechat_article_by_title/",
        async: false,
        data: {title: article_title},
        success: function (data) {
            console.log('服务器响应:', data);
            if (data.status === 'ok') {
                var data_list = data.data;
                console.log('获取到的数据:', data_list);
                if (data_list.length>0){
                    html_content = data_list[0].html_content;
                    console.log('原始HTML内容长度:', html_content.length);

                    // 处理微信图片URL
                    html_content = processWechatImages(html_content);
                    console.log('处理后HTML内容长度:', html_content.length);

                    // 保存文章ID到Cookie以便后续操作
                    if (data_list[0].article_id) {
                        $.cookie('wechat_article_id', data_list[0].article_id, { expires: 1 });
                        currentArticleId = data_list[0].article_id;
                        console.log('保存文章ID到Cookie:', data_list[0].article_id);
                    }
                }
            } else {
                var err_msg  = data.msg ? data.msg : '内部服务错误';
                console.log('服务器错误:', err_msg);
                show_gitter('错误提示', err_msg, 2);
            }
        },
        error: function(xhr, status, error) {
            console.log('AJAX请求失败:', error);
            console.log('状态:', status);
            console.log('响应:', xhr.responseText);
        }
    });
    console.log('最终HTML内容:', html_content);
    console.log('========================');

    if (instance != null) {
        tinyMCE.activeEditor.setContent(html_content);
    }
    $('#old_content').val(html_content);
}

// 处理微信图片URL
function processWechatImages(html_content) {
    console.log('=== 处理微信图片URL ===');

    // 查找所有微信图片URL
    const wechatImageRegex = /https?:\/\/mmbiz\.qpic\.cn\/[^"'\s>]+/g;
    const matches = html_content.match(wechatImageRegex);

    if (matches) {
        console.log('找到微信图片:', matches.length, '张');
        matches.forEach((imageUrl, index) => {
            console.log(`图片 ${index + 1}:`, imageUrl);

            // 方案1：使用代理服务
            const proxyUrl = `/apis/wechat_image_proxy/?url=${encodeURIComponent(imageUrl)}`;
            html_content = html_content.replace(imageUrl, proxyUrl);

            // 方案2：如果代理失败，可以尝试其他处理方式
            // 例如：显示占位图片或提示用户手动上传
        });
        console.log('已替换所有微信图片URL为代理URL');
    } else {
        console.log('未找到微信图片');
    }

    console.log('========================');
    return html_content;
}

// 替换编辑器中的微信图片
function replaceWechatImages() {
    if (!tinyMCE.activeEditor) {
        show_gitter('提示', '编辑器未初始化', 2);
        return;
    }

    const content = tinyMCE.activeEditor.getContent();
    const wechatImageRegex = /https?:\/\/mmbiz\.qpic\.cn\/[^"'\s>]+/g;
    const matches = content.match(wechatImageRegex);

    if (!matches || matches.length === 0) {
        show_gitter('提示', '未找到微信图片', 1);
        return;
    }

    let message = `找到 ${matches.length} 张微信图片，将自动替换为代理链接。\n\n`;
    message += '如果图片仍无法显示，建议：\n';
    message += '1. 手动上传图片到服务器\n';
    message += '2. 使用编辑器的图片上传功能\n';
    message += '3. 联系管理员配置图片代理服务\n\n';
    message += '是否继续替换？';

    if (confirm(message)) {
        let newContent = content;
        matches.forEach((imageUrl, index) => {
            const proxyUrl = `/apis/wechat_image_proxy/?url=${encodeURIComponent(imageUrl)}`;
            newContent = newContent.replace(imageUrl, proxyUrl);
        });

        tinyMCE.activeEditor.setContent(newContent);
        show_gitter('成功', `已替换 ${matches.length} 张微信图片`, 1);
    }
}

// 显示文章信息
function displayArticleInfo(sections) {
    if (sections.length === 0) return;
    
    const firstSection = sections[0];
    $('#article-name').text(firstSection.title || '无标题');
    $('#article-title').text(firstSection.title || '无标题');
    $('#article-id').text(currentArticleId);
    $('#sections-count').text(sections.length);
    $('#created-time').text(new Date(firstSection.created_at).toLocaleString());
    $('#product-title').val(firstSection.title || '');
    
    $('#article-info').show();
}



/*保存文章内容 - 支持文章ID和标题两种方式，优先使用标题*/
function update_detail() {
    var article_id = $.cookie('wechat_article_id');
    var article_title = $.cookie('wechat_article_title');

    console.log('=== update_detail函数调试信息 ===');
    console.log('当前全局变量 currentArticleTitle:', currentArticleTitle);
    console.log('当前全局变量 currentArticleId:', currentArticleId);
    console.log('Cookie中的article_title:', article_title);
    console.log('Cookie中的article_id:', article_id);

    // 优先使用标题，如果没有则使用文章ID
    if (currentArticleTitle) {
        console.log('使用全局变量中的文章标题保存:', currentArticleTitle);
        updateByArticleTitle(currentArticleTitle);
    } else if (article_title && article_title !== '') {
        console.log('使用Cookie中的文章标题保存:', article_title);
        updateByArticleTitle(article_title);
    } else if (currentArticleId) {
        console.log('使用全局变量中的文章ID保存:', currentArticleId);
        updateByArticleId(currentArticleId);
    } else if (article_id && article_id !== '') {
        console.log('使用Cookie中的文章ID保存:', article_id);
        updateByArticleId(article_id);
    } else {
        console.log('错误：没有找到文章ID或标题');
        show_gitter('提示信息', '信息过期，请返回重新进入', 2);
        return;
    }
    console.log('========================');
}

// 通过文章ID保存
function updateByArticleId(article_id) {
    var html_content = tinyMCE.activeEditor.getContent();
    var old_content = $('#old_content').val();

    if (!html_content.trim()) {
        show_gitter('提示信息', '内容不能为空', 2);
        return;
    }

    var filters = JSON.stringify({article_id: article_id});

    $.ajax({
        type: "POST",
        url: "/apis/update_wechat_article/",
        async: false,
        data: {
            filters: filters,
            html_content: html_content
        },
        success: function (data) {
            if (data.status === 'ok') {
                $('#old_content').val(html_content);
                show_gitter('成功', '文章内容保存成功！', 1);
            } else {
                show_gitter('错误', data.msg || '保存失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，保存失败', 2);
        }
    });
}

// 通过文章标题保存
function updateByArticleTitle(article_title) {
    var html_content = tinyMCE.activeEditor.getContent();
    var old_content = $('#old_content').val();

    if (!html_content.trim()) {
        show_gitter('提示信息', '内容不能为空', 2);
        return;
    }

    $.ajax({
        type: "POST",
        url: "/apis/update_wechat_article_by_title/",
        async: false,
        data: {
            title: article_title,
            html_content: html_content
        },
        success: function (data) {
            if (data.status === 'ok') {
                $('#old_content').val(html_content);
                show_gitter('成功', '文章内容保存成功！', 1);
                // 如果返回了文章ID，保存到Cookie
                if (data.article_id) {
                    $.cookie('wechat_article_id', data.article_id, { expires: 1 });
                    currentArticleId = data.article_id;
                }
            } else {
                show_gitter('错误', data.msg || '保存失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，保存失败', 2);
        }
    });
}

// 生成产品页面
function make_product_html() {
    const productTitle = $('#product-title').val();
    const productType = $('#product-type').val();
    const productLang = $('#product-lang').val();
    const sel_paib = $('#sel_paib').val();

    if (!productTitle.trim()) {
        show_gitter('提示信息', '请输入产品标题', 2);
        return;
    }

    if (!currentArticleId && !currentArticleTitle) {
        show_gitter('提示信息', '缺少文章ID或标题', 2);
        return;
    }

    // 构建请求数据
    const requestData = {
        product_title: productTitle,
        product_type: productType,
        lang: productLang,
        paib: sel_paib
    };

    // 优先使用文章ID，如果没有则使用标题
    if (currentArticleId) {
        requestData.article_id = currentArticleId;
    } else if (currentArticleTitle) {
        requestData.article_title = currentArticleTitle;
    }

    console.log('生成产品请求数据:', requestData);

    $.ajax({
        type: "POST",
        url: "/apis/generate_product_from_wechat/",
        data: requestData,
        success: function (data) {
            if (data.status === 'ok') {
                show_gitter('成功', `产品页面生成成功！产品ID: ${data.product_id}`, 1);
                $('#makeHtml').modal('hide');

                // 询问是否跳转到产品管理页面
                setTimeout(() => {
                    if (confirm('是否跳转到产品管理页面查看生成的产品？')) {
                        window.open('/admin1/product.html', '_blank');
                    }
                }, 1500);
            } else {
                show_gitter('错误', data.msg || '生成产品页面失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，生成产品页面失败', 2);
        }
    });
}

// 显示消息提示
function show_gitter(title, text, type) {
    $.gritter.add({
        title: title,
        text: text,
        sticky: false,
        time: type === 1 ? 3000 : 5000,
        class_name: type === 1 ? 'gritter-success' : 'gritter-error'
    });
}

/*填入初始数据*/
//tinyMCE.activeEditor.setContent("<h1>测试</h1><hr><h2>这是测试的数据<h2>");
/*
1、如果当前页面只有一个编辑器：
    获取内容：tinyMCE.activeEditor.getContent()
    设置内容：tinyMCE.activeEditor.setContent("需要设置的编辑器内容")
2、如果当前页面有多个编辑器（下面的"[0]"表示第一个编辑器，以此类推）：
    获取内容：tinyMCE.editors[0].getContent()
    设置内容：tinyMCE.editors[0].setContent("需要设置的编辑器内容")
*/
function setcontent() {
    tinyMCE.activeEditor.setContent("<h1>设置内容1</h1>");
    //tinyMCE.editors[0].setContent("<h1>设置内容2</h1>");
}

function getcontent() {
    alert(tinyMCE.activeEditor.getContent());
}

/*3、获取不带HTML标记的纯文本内容：
 var activeEditor = tinymce.activeEditor;
 var editBody = activeEditor.getBody();
 activeEditor.selection.select(editBody);
 var text = activeEditor.selection.getContent( {'format' : 'text' } );*/
function getbody() {
    var activeEditor = tinymce.activeEditor;
    var editBody = activeEditor.getBody();
    activeEditor.selection.select(editBody);
    var text = activeEditor.selection.getContent({'format': 'text'});
    alert(text);
}

// 生成文章详情页
function generateArticlePage() {
    // 获取当前文章标题
    var article_title = $.cookie('wechat_article_title') || currentArticleTitle;
    var article_id = $.cookie('wechat_article_id') || currentArticleId;

    if (!article_title && !article_id) {
        show_gitter('提示信息', '缺少文章标题或ID', 2);
        return;
    }

    // 确认生成
    if (!confirm('确定要生成文章详情页吗？这将创建一个支持中英文切换的静态HTML页面。')) {
        return;
    }

    // 构建请求数据
    const requestData = {};

    // 优先使用标题
    if (article_title) {
        requestData.title = article_title;
    } else if (article_id) {
        requestData.article_id = article_id;
    }

    console.log('生成文章详情页请求数据:', requestData);

    // 显示加载状态
    show_gitter('提示', '正在生成文章详情页，请稍候...', 1);

    $.ajax({
        type: "POST",
        url: "/apis/generate_wechat_article_page/",
        data: requestData,
        success: function (data) {
            if (data.status === 'ok') {
                show_gitter('成功', '文章详情页生成成功！', 1);

                // 显示生成的文件链接
                let message = '生成成功！<br>';
                if (data.files.chinese) {
                    message += `中文页面: <a href="${data.files.chinese}" target="_blank">${data.files.chinese}</a><br>`;
                }
                if (data.files.english) {
                    message += `英文页面: <a href="${data.files.english}" target="_blank">${data.files.english}</a>`;
                }

                // 使用更详细的成功消息
                setTimeout(() => {
                    show_gitter('生成完成', message, 1);
                }, 1000);

                // 询问是否打开生成的页面
                setTimeout(() => {
                    if (data.files.chinese && confirm('是否打开生成的中文页面？')) {
                        window.open(data.files.chinese, '_blank');
                    }
                }, 2000);
            } else {
                show_gitter('错误', data.msg || '生成文章详情页失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，生成文章详情页失败', 2);
        }
    });
}
</script>

<!--end-main-container-part-->
<script src="/admin/js/jquery.i18n.js"></script>
<script src="/admin/js/language.js"></script>
<!--Footer-part-->

<script src="/admin/js/footer.js"></script>
<!--end-Footer-part-->

<script src="/admin/js/excanvas.min.js"></script>
<!--<script src="/admin/js/jquery.min.js"></script>-->
<script src="/admin/js/jquery.ui.custom.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<!--<script src="/admin/js/jquery.flot.min.js"></script>-->
<!--<script src="/admin/js/jquery.flot.resize.min.js"></script>-->
<script src="/admin/js/jquery.peity.min.js"></script>
<script src="/admin/js/fullcalendar.min.js"></script>
<script src="/admin/js/matrix.js"></script>
<!--<script src="/admin/js/matrix.dashboard.js"></script>-->
<script src="/admin/js/jquery.gritter.min.js"></script>
<script src="/admin/js/matrix.interface.js"></script>
<script src="/admin/js/matrix.chat.js"></script>
<script src="/admin/js/jquery.validate.js"></script>
<script src="/admin/js/matrix.form_validation.js"></script>
<script src="/admin/js/jquery.wizard.js"></script>
<script src="/admin/js/jquery.uniform.js"></script>
<!--<script src="/admin/js/select2.min.js"></script>-->
<script src="/admin/js/matrix.popover.js"></script>
<script src="/admin/js/jquery.dataTables.min.js"></script>
<!--<script src="/admin/js/matrix.tables.js"></script>-->
<script src="/admin/js/page.js"></script>
<!--<script src="/admin/js/matrix.interface.js"></script>-->

</body>
</html>
